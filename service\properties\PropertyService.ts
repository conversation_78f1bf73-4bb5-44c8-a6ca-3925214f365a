import { Request } from "express";
import { PoolClient } from "pg";
import { db } from "../../config/database";
import { sanitizeAndValidate } from "../../utils/validations/property.validation";
import { generateUniqueSlug } from "../../utils/helperFunctions/generateUniqueSlug";
import {
  deleteFileFromS3,
  uploadFileToS3,
} from "../../utils/services/s3-bucket";
import fs from "fs";
import path from "path";
import { promisify } from "util";
import {
  PropertyRepository,
  CreateOrUpdateResult,
} from "../../repo/properties/PropertyRepository";
import {
  FeaturesArraySchema,
  formatZodError,
  PropertyUpsertSchema,
} from "../../dto/property/CreateUpdatePropertyDTO";
import { LocationService } from "../locations/LocationService";
import { FilterParams } from "../../dto/payment/FilterParamTypes";
import { PropertyHelper } from "../../utils/helperFunctions/property/PropertyHelper";
import { AppError } from "../../utils/errors/ValidationError";
import { NoteRepository } from "../../repo/look/note-repository";

const unlinkAsync = promisify(fs.unlink);

export class PropertyService {
  private repo = new PropertyRepository();
  private noteRepo = new NoteRepository();
  private locationService = new LocationService();

 

  // -------- list + filters --------
  async getAllProperties(query: any) {
    const page = PropertyHelper.toInt(query.page ?? 1, 1);
    const pageSize = PropertyHelper.toInt(
      query.pageSize ?? query.limit ?? 10,
      10
    );

    const filters: FilterParams = {
      statusName: (query.status ?? "").toString().trim() || null,
      propertyTypeId: query.propertyTypeId
        ? Number(query.propertyTypeId)
        : null,
      locationId: query.locationId ? Number(query.locationId) : null,
      listingType: query.listingType ? Number(query.listingType) : null,
      search: (query.search ?? "").toString().trim() || null,
      page,
      pageSize,
    };

    // resolve statusId in repo (DB), then list
    const statusId = await this.repo.findStatusIdByName(filters.statusName);
    return this.repo.getFilteredProperties({ ...filters, statusId });
  }

  async getPropertyById(id: number) {
    const row = await this.repo.getPropertyById(id);
    if (!row) throw new AppError("Property not found", 404);
    return row;
  }

  async getPropertyWithOwnerById(id: number) {
    const row = await this.repo.getPropertyWithOwnerById(id);
    if (!row) throw new AppError("Property not found", 404);
    return row;
  }

  // -------- create/update with uploads --------
  async createOrUpdateProperty(req: Request) {
    const { locationId, expiryDate, permitId } = req.body;

    const locationRes = await this.locationService.getLocationById(locationId);

    const locationName = locationRes.name?.trim().toLowerCase();

    if (locationName === "abu dhabi") {
      const fileGroups = req.files as {
        [fieldname: string]: Express.Multer.File[];
      };

      const govtQrFile = fileGroups?.["govtIssuedQr"]?.[0];
      const photoFiles = fileGroups?.["propertyPhotos"] || [];

      const missingFields = [];
      if (!expiryDate) missingFields.push("expiryDate");
      if (!permitId) missingFields.push("permitId");
      if (!govtQrFile) missingFields.push("govtIssuedQr");

      if (missingFields.length > 0) {
        // 🧹 Clean up files if uploaded
        if (govtQrFile) {
          try {
            await unlinkAsync(
              path.join("public", "documents", govtQrFile.filename)
            );
          } catch (err) {
            console.error("Error deleting QR file:", err);
          }
        }
        for (const photo of photoFiles) {
          try {
            await unlinkAsync(path.join("public", "documents", photo.filename));
          } catch (err) {
            console.error("Error deleting photo file:", err);
          }
        }

        throw new AppError(
          `Missing required field(s) for Abu Dhabi: ${missingFields.join(
            ", "
          )}`,
          400
        );
      }
    }

    const client: PoolClient = await db.connect();

    // 2) Zod validation (DTO): catches wrong types/ranges/missing required
    const parsed = PropertyUpsertSchema.safeParse(req.body);
    if (!parsed.success) {
      throw new AppError(
        JSON.stringify({ errors: formatZodError(parsed.error) }),
        422
      );
    }

    const body = req.body;

    // 3) Repo lookups
    const profileId = req.user?.id;
    const agencyId = await this.repo.getAgencyIdByProfileId(profileId);
    const accountType = await this.repo.getAccountTypeByProfileId(profileId);
    const userType = accountType === "Individual" ? "agent" : "agency";
    const verifiedStatusId = await this.repo.getVerifiedStatusId(["Verified"]);
    const pendingStatusId = await this.repo.getPendingStatusId();
    const loginId = await this.repo.getLoginIdByProfileId(profileId);

    // 4) Files
    const fileGroups = (req.files || {}) as {
      [field: string]: Express.Multer.File[];
    };
    const govtQrFile = fileGroups["govtIssuedQr"]?.[0] || null;
    const photoFiles = fileGroups["propertyPhotos"] || [];

    let uploadedQrKey: string | null = null;
    let uploadedPhotoKeys: string[] = [];

    // 5) Slugs/codes
    const slug = await generateUniqueSlug(
      body.name,
      "agn.properties",
      "slug",
      body.id ?? undefined
    );
    const code = await generateUniqueSlug(
      body.name,
      "agn.properties",
      "slug",
      body.id ?? undefined
    );

    // 6) Features: normalize then validate as array
    const featuresArray = PropertyHelper.splitCsv(body.features);
    const featValidated = FeaturesArraySchema.safeParse(featuresArray);
    if (!featValidated.success) {
      throw new AppError(
        JSON.stringify({ errors: formatZodError(featValidated.error) }),
        422
      );
    }
    const features = featValidated.data;

    try {
      await client.query("BEGIN");

      // Uploads
      if (govtQrFile) {
        const { fileKey } = await uploadFileToS3(
          govtQrFile.filename,
          govtQrFile,
          userType,
          "property-qr"
        );
        uploadedQrKey = fileKey;
      }
      if (photoFiles.length > 0) {
        const uploaded = await Promise.all(
          photoFiles.map((f) =>
            uploadFileToS3(f.filename, f, userType, "property-photo")
          )
        );
        uploadedPhotoKeys = uploaded.map((u) => u.fileKey);
      }

      // Payload for DB (only DB-facing fields)
      const payload = {
        ...body,
        slug,
        code,
        createdBy: profileId || 1,
        agencyId,
        statusId: body.id ? body.statusId ?? undefined : pendingStatusId, // default Pending only on create
        govtIssuedQr: uploadedQrKey ?? body.govtIssuedQr ?? null,
      };

      // Create/update property
      const created: CreateOrUpdateResult =
        await this.repo.createOrUpdateProperty(client, payload);

      // Images bulk
      if (uploadedPhotoKeys.length) {
        await this.repo.insertPropertyImagesBulk(client, {
          propertyId: created.id,
          mediaTypeId: 48,
          statusId: verifiedStatusId,
          loginId,
          keys: uploadedPhotoKeys,
        });
      }

      // Features bulk
      if (features.length) {
        await this.repo.upsertFeaturesBulk(client, {
          propertyId: created.id,
          statusId: verifiedStatusId,
          createdBy: payload.createdBy,
          features,
        });
      }

      await client.query("COMMIT");
      return created;
    } catch (e: any) {
      await client.query("ROLLBACK");
      const cleanup = [uploadedQrKey, ...uploadedPhotoKeys].filter(
        Boolean
      ) as string[];
      await Promise.allSettled(cleanup.map((key) => deleteFileFromS3(key)));
      throw new AppError(e?.message || "Failed to save property", 500);
    } finally {
      client.release();
    }
  }

  async updatePropertyStatus(id: number, status: string, reason?: string, adminId?: number) {
    let statusId: number;
    let action = "status_update";

    if (status.toLowerCase() === "unblock") {
      // Get the previous status from history
      const previousStatus = await this.repo.getPreviousStatusForProperty(id);
      if (!previousStatus) {
        throw new AppError(`No previous status found for property ${id}. Cannot unblock.`, 400);
      }
      statusId = previousStatus;
      // keep action as 'status_update' so history records are consistent
    } else {
      const foundStatusId = await this.repo.findStatusIdByName(status);
      if (!foundStatusId) throw new AppError(`Status '${status}' not found.`, 404);
      statusId = foundStatusId;
    }

    // Get current status before updating
    const currentStatus = await this.repo.getCurrentPropertyStatus(id);

    await this.repo.updateStatusById(id, statusId, reason);

    // Log property action if adminId provided
    if (adminId) {
      await this.repo.logPropertyAction(
        id,
        action,
        currentStatus,
        statusId,
        reason || null,
        adminId
      );
    }
  }

  async bulkUpdatePropertyStatus(ids: number[], status: string, reason?: string, adminId?: number) {
    if (status.toLowerCase() === "unblock") {
      // Handle unblock individually for each property
      for (const id of ids) {
        await this.updatePropertyStatus(id, status, reason, adminId);
      }
      return;
    }

    const statusId = await this.repo.findStatusIdByName(status);
    if (!statusId) throw new AppError(`Status '${status}' not found.`, 404);
    
    const client: PoolClient = await db.connect();
    try {
      await client.query("BEGIN");

      // Get current statuses for all properties before updating
      const currentStatuses: { [key: number]: number | null } = {};
      for (const id of ids) {
        currentStatuses[id] = await this.repo.getCurrentPropertyStatus(id);
      }

      await this.repo.bulkUpdateStatusByIds(client, ids, statusId, reason);
      await client.query("COMMIT");

      // Log property actions if adminId provided
      if (adminId) {
        for (const id of ids) {
          await this.repo.logPropertyAction(
            id,
            "status_update",
            currentStatuses[id],
            statusId,
            reason || null,
            adminId
          );
        }
      }
    } catch (err) {
      await client.query("ROLLBACK");
      throw err;
    } finally {
      client.release();
    }
  }

  async togglePropertyFlag(id: number, column: "isFeatured" | "isVerified") {
    return this.repo.toggleFlag(id, column);
  }

  // delete property with children: features + images; then remove S3 files
  async deleteProperty(id: number) {
    if (!Number.isFinite(id)) throw new AppError("Invalid property id.", 400);

    const client: PoolClient = await db.connect();
    let imageKeys: string[] = [];

    try {
      await client.query("BEGIN");

      // 1) Read image keys BEFORE deleting rows, so we can delete from S3 after commit
      imageKeys = await this.repo.getImageKeysByPropertyId(client, id);

      // 2) Delete children first to satisfy FKs, then delete property
      await this.repo.deleteFeaturesByPropertyId(client, id);
      await this.repo.deleteImagesByPropertyId(client, id);
      await this.repo.nullifyListingsByPropertyId(client, id);
      const deleted = await this.repo.deleteProperty(client, id);

      if (!deleted) {
        throw new AppError("Property not found or already deleted", 404);
      }

      await client.query("COMMIT");
    } catch (err: any) {
      await client.query("ROLLBACK");
      // if this was a FK error or anything else, surface a clean message
      throw new AppError(
        err?.message || "Failed to delete property. Please try again.",
        err?.statusCode || 500
      );
    } finally {
      client.release();
    }

    // 3) Best-effort S3 cleanup AFTER DB commit (to avoid inconsistent state)
    if (imageKeys.length) {
      await Promise.allSettled(imageKeys.map((k) => deleteFileFromS3(k)));
    }
  }

  async createPropertyNote(propertyId: number, note: string, userId: number) {
    if (!note || !note.trim()) throw new AppError("Note cannot be empty.", 400);
    return this.noteRepo.createNote(propertyId, note, "property");
  }

  async getNotesOfProperty(propertyId: number) {
    return this.noteRepo.getNotesByEntityId(propertyId, "property");
  }

  async getFeaturedProperties(limit: number) {
    return this.repo.getFeaturedProperties(limit);
  }

  async searchPublicProperties(query: any) {
    // parse in service
    const params = {
      location: query.location ? Number(query.location) : null,
      minPrice: query.min_price ? Number(query.min_price) : null,
      maxPrice: query.max_price ? Number(query.max_price) : null,
      type: query.type ? Number(query.type) : null,
      bedrooms: query.bedrooms ? Number(query.bedrooms) : null,
      bathrooms: query.bathrooms ? Number(query.bathrooms) : null,
      page: PropertyHelper.toInt(query.page ?? 1, 1),
      pageSize: PropertyHelper.toInt(query.limit ?? 10, 10),
    };
    return this.repo.searchPublicProperties(params);
  }

  async getPublicPropertyBySlug(slug: string) {
    return this.repo.getPropertyBySlug(slug);
  }

  async updatePropertyPhotos(req: Request) {
    const propertyId = Number(req.params.propertyId);
    if (!Number.isFinite(propertyId))
      throw new AppError("Invalid property id.", 400);

    // parse payload (delete list and uploads)
    const photoIdsToDelete = PropertyHelper.parseIdList(
      req.body.photoIdsToDelete
    );
    const files =
      (req.files as { [field: string]: Express.Multer.File[] } | undefined)?.[
      "propertyPhotos"
      ] || [];

    // who’s acting? (we’ll use for createdBy & userType folder)
    const profileId = req.user?.id;
    const accountType = await this.repo.getAccountTypeByProfileId(profileId);
    const userType = accountType === "Individual" ? "agent" : "agency";

    // prefetch login id for createdBy
    const loginId = await this.repo.getLoginIdByProfileId(profileId);

    // status/mediaType for images
    const statusId = await this.repo.getVerifiedStatusId(["Verified"]); // or a generic “Active” status
    const mediaTypeId = 48; // your existing mediaType for property photos

    const client: PoolClient = await db.connect();
    const uploadedKeys: string[] = [];

    try {
      await client.query("BEGIN");

      // 1) Delete/unlink existing media rows (agn.images) for this property
      if (photoIdsToDelete.length) {
        // if you also want to delete S3 files, first fetch keys:
        const keysToRemove = await this.repo.getImageKeysByIds(
          client,
          propertyId,
          photoIdsToDelete
        );
        // Delete rows first (unlink)
        await this.repo.deleteImagesByIds(client, propertyId, photoIdsToDelete);

        // OPTIONAL: hard-delete from S3 too (uncomment if you want actual S3 removal)
        // await Promise.all(keysToRemove.map((k) => deleteFileFromS3(k)));
      }

      // 2) Upload new files to S3
      if (files.length) {
        const uploads = await Promise.all(
          files.map((f) =>
            uploadFileToS3(f.filename, f, userType, "property-photo")
          )
        );
        uploadedKeys.push(...uploads.map((u) => u.fileKey));

        // 3) Insert DB rows for new files (bulk, single SQL)
        await this.repo.insertPropertyImagesBulk(client, {
          propertyId,
          statusId,
          mediaTypeId,
          loginId: loginId ?? profileId ?? 1,
          keys: uploadedKeys,
        });
      }

      await client.query("COMMIT");

      // Return the updated property with its media
      return this.repo.getPropertyById(propertyId);
    } catch (err: any) {
      await client.query("ROLLBACK");

      // cleanup any files that were uploaded in this attempt
      if (uploadedKeys.length) {
        await Promise.all(uploadedKeys.map((k) => deleteFileFromS3(k))).catch(
          () => void 0
        );
      }

      throw new AppError(
        err?.message || "Failed to update property photos.",
        500
      );
    } finally {
      client.release();
    }
  }

  async getFilterOptions() {
    const [propertyTypes, locations, listingTypes, agents, statuses] = await Promise.all([
      this.repo.getPropertyTypes(),
      this.repo.getLocations(),
      this.repo.getListingTypes(),
      this.repo.getAgents(),
      this.repo.getStatuses(),
    ]);

    return {
      propertyTypes,
      locations,
      listingTypes,
      agents,
      statuses,
    };
  }

  async bulkDeleteProperties(ids: number[]) {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error("Property IDs array is required");
    }

    const client: PoolClient = await db.connect();
    let allImageKeys: string[] = [];

    try {
      await client.query("BEGIN");

      // 1) Collect all image keys BEFORE deleting rows
      for (const id of ids) {
        const imageKeys = await this.repo.getImageKeysByPropertyId(client, id);
        allImageKeys.push(...imageKeys);
      }

      // 2) Delete children first to satisfy FKs, then delete properties
      for (const id of ids) {
        await this.repo.deleteFeaturesByPropertyId(client, id);
        await this.repo.deleteImagesByPropertyId(client, id);
        await this.repo.nullifyListingsByPropertyId(client, id);
      }

      const deletedCount = await this.repo.bulkDeleteProperties(client, ids);

      if (deletedCount === 0) {
        throw new Error("No properties found or already deleted");
      }

      await client.query("COMMIT");

      // TODO: Delete images from S3 after successful commit
      // This would require implementing S3 bulk delete
    } catch (err: any) {
      await client.query("ROLLBACK");
      // if this was a FK error or anything else, surface a clean message
      throw new AppError(
        err?.message || "Failed to delete properties. Please try again.",
        err?.statusCode || 500
      );
    } finally {
      client.release();
    }
  }

  async getCurrentPropertyStatus(propertyId: number): Promise<number | null> {
    return this.repo.getCurrentPropertyStatus(propertyId);
  }

  async getPropertyHistory(propertyId: number) {
    return this.repo.getPropertyHistory(propertyId);
  }

  async getPreviousStatusForProperty(propertyId: number): Promise<number | null> {
    return this.repo.getPreviousStatusForProperty(propertyId);
  }

  async getStatusNameById(statusId: number): Promise<string | null> {
    const { rows } = await db.query('SELECT name FROM look.status WHERE id = $1', [statusId]);
    return rows.length > 0 ? rows[0].name : null;
  }
}
