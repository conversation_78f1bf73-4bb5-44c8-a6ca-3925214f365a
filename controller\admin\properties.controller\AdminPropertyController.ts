import { Request, Response } from "express";
import { PropertyService } from "../../../service/PropertyService";
import asyncHandler from "../../../middleware/trycatch";
import { response, responseData } from "../../../utils/response";

export class AdminPropertyController {
  private propertyService = new PropertyService();

  getAllProperties = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.propertyService.getAllProperties(req.query);
    return responseData(res, 200, "Properties fetched successfully", result);
  });

  getPropertyById = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.propertyService.getPropertyById(
      Number(req.params.id)
    );
    return responseData(res, 200, "Property fetched successfully", result);
  });

  updateStatus = asyncHandler(async (req: Request, res: Response) => {
    await this.propertyService.updatePropertyStatus(
      Number(req.params.id),
      req.body.status
    );
    return response(res, 200, "Status updated successfully");
  });

  bulkUpdateStatus = asyncHandler(async (req: Request, res: Response) => {
    const { ids, status, reason } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return response(res, 400, "Property IDs array is required");
    }
    
    if (!status) {
      return response(res, 400, "Status is required");
    }

    // Validate and convert IDs to numbers
    const validIds: number[] = [];
    for (const id of ids) {
      const numId = Number(id);
      if (!Number.isFinite(numId) || numId <= 0) {
        return response(res, 400, `Invalid property ID: ${id}`);
      }
      validIds.push(numId);
    }

    await this.propertyService.bulkUpdatePropertyStatus(validIds, status, reason);
    return response(res, 200, `Status updated successfully for ${validIds.length} properties`);
  });

  bulkDeleteProperties = asyncHandler(async (req: Request, res: Response) => {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return response(res, 400, "Property IDs array is required");
    }

    // Validate and convert IDs to numbers
    const validIds: number[] = [];
    for (const id of ids) {
      const numId = Number(id);
      if (!Number.isFinite(numId) || numId <= 0) {
        return response(res, 400, `Invalid property ID: ${id}`);
      }
      validIds.push(numId);
    }

    await this.propertyService.bulkDeleteProperties(validIds);
    return response(res, 200, `Successfully deleted ${validIds.length} properties`);
  });

  toggleFlag = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.propertyService.togglePropertyFlag(
      Number(req.params.id),
      req.body.column
    );
    return response(res, 200, result);
  });

  deleteProperty = asyncHandler(async (req: Request, res: Response) => {
    await this.propertyService.deleteProperty(Number(req.params.id));
    return response(res, 200, "Property deleted successfully.");
  });

  createNote = asyncHandler(async (req: Request, res: Response) => {
    const propertyId = Number(req.params.id);
    const { note } = req.body;

    const result = await this.propertyService.createPropertyNote(
      propertyId,
      note,
      req.user?.id || 1
    );

    return responseData(res, 201, "Note added successfully.", result);
  });

  getNotes = asyncHandler(async (req: Request, res: Response) => {
    const propertyId = Number(req.params.id);
    const result = await this.propertyService.getNotesOfProperty(propertyId);
    return responseData(res, 200, "Notes fetched successfully.", result);
  });
}
