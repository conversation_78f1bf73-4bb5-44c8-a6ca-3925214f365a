import { Request, Response } from "express";
import { errorCatchResponse, responseData } from "../../utils/response";
import asyncHandler from "../../middleware/trycatch";
import { StatusService } from "../../service/status/StatusService";

const statusService = new StatusService();

export const getAllStatus = asyncHandler(async (req: Request, res: Response) => {
  try {
    const rows = await statusService.getAllStatuses();
    return responseData(res, 200, "Status fetched successfully", rows);
  } catch (error) {
    console.error("Error fetching Status:", error);
    return errorCatchResponse(res, "Something went wrong");
  }
});

export const searchAgencies = asyncHandler(async (req: Request, res: Response) => {
  try {
    const { search } = req.query;
    const searchTerm = search ? search.toString() : '';
    const rows = await statusService.searchAgencies(searchTerm);
    return responseData(res, 200, "Agencies fetched successfully", rows);
  } catch (error) {
    console.error("Error fetching agencies:", error);
    return errorCatchResponse(res, "Something went wrong");
  }
});