// File: services/PropertyService.ts
import { Request } from "express";
import { PropertyRepository } from "../repo/PropertyRepository";
import { sanitizeAndValidate } from "../utils/validations/property.validation";
import {db }from "../config/database";
import { TABLE } from "../utils/database/table";
import { AUTH } from "../utils/database/queries/auth";
import {
  deleteFileFromS3,
  uploadFileToS3,
} from "../utils/services/s3-bucket";
import { generateUniqueSlug } from "../utils/helperFunctions/generateUniqueSlug";
import { PoolClient } from "pg";
import { AppError } from "../utils/error";
import { NoteRepository } from "../repo/NoteRepository";
import { PropertyHelper } from "../utils/helperFunctions/properties/PropertyHelper";

export class PropertyService {
  private repo = new PropertyRepository();
  private noteRepo = new NoteRepository();

  async getAllProperties(query: any) {
  const filters = {
    statusId: PropertyHelper.safeNumber(query.statusId),
    propertyTypeId: PropertyHelper.safeNumber(query.propertyTypeId),
    locationId: PropertyHelper.safeNumber(query.locationId),
    listingType: PropertyHelper.safeNumber(query.listingType),
    page: PropertyHelper.toInt(query.page, 1),
    pageSize: PropertyHelper.toInt(query.pageSize ?? query.limit, 10),
  };

  return this.repo.getFilteredProperties(filters);
}


  async getPropertyById(id: number) {
    const property = await this.repo.getPropertyById(id);
    if (!property) throw new Error("Property not found");
    return property;
  }

  async createOrUpdateProperty(req: Request) {
    const client: PoolClient = await db.connect();
    const sanitized = await sanitizeAndValidate(req.body, {
      nonNullableKeys: [
        "code",
        "name",
        "locationId",
        "price",
        "size",
        "listingType",
        "createdBy",
      ],
      booleanKeys: [
        "parking",
        "swimmingPools",
        "gym",
        "isFeatured",
        "isVerified",
        "furnished",
      ],
    });

    const profileId = req.user?.id;
    const agencyRes = await db.query(
      `SELECT * FROM agn.agencies WHERE "profileId" = $1 LIMIT 1`,
      [profileId]
    );
    if (agencyRes.rowCount === 0)
      throw new AppError("Agency not found for profile", 404);
    const agencyId = agencyRes.rows[0].id;

    const profileRows = await db.query(AUTH.SELECT_BY_ID, [profileId]);
    const accountType = profileRows.rows[0]?.accountType;
    const userType = accountType === "Individual" ? "agent" : "agency";

    const fileGroups = req.files as {
      [fieldname: string]: Express.Multer.File[];
    };
    const govtQrFile = fileGroups["govtIssuedQr"]?.[0] || null;
    const photoFiles = fileGroups["propertyPhotos"] || [];

    let uploadedQrKey: string | null = null;
    let uploadedPhotoKeys: string[] = [];

    try {
      await client.query("BEGIN");

      // Upload QR
      if (govtQrFile) {
        const qrRes = await uploadFileToS3(
          govtQrFile.filename,
          govtQrFile,
          userType,
          "property-qr"
        );
        uploadedQrKey = qrRes.fileKey;
        sanitized.govtIssuedQr = uploadedQrKey;
      }

      // Upload Photos
      if (photoFiles.length > 0) {
        const photoRes = await Promise.all(
          photoFiles.map((file) =>
            uploadFileToS3(file.filename, file, userType, "property-photo")
          )
        );
        uploadedPhotoKeys = photoRes.map((r) => r.fileKey);
      }

      // Prepare values
      const slug = await generateUniqueSlug(
        sanitized.name,
        "agn.properties",
        "slug",
        sanitized.id
      );
      sanitized.slug = slug;
      sanitized.createdBy = profileId || 1;
      sanitized.agencyId = agencyId;

      // Get "Pending" status
      const statusRow = await db.query(
        `SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1`,
        ["Pending"]
      );
      if (statusRow.rowCount === 0) throw new Error("Pending status not found");
      sanitized.statusId = statusRow.rows[0].id;

      // Get "Verified" status
      const statusNames = ["Verified"];
      const approvedStatus = await db.query(
        AUTH.SELECT_ACCOUNT_STATUS(statusNames),
        statusNames
      );
      const verifiedStatusId = approvedStatus.rows[0]?.id;
      if (!verifiedStatusId) throw new Error("Verified status not found");

      // Get login ID
      const loginRes = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
        profileId,
      ]);
      const loginId = loginRes.rows[0].id;

      // 🔄 Use new repo method
      const property = await this.repo.savePropertyWithAssets(
        sanitized,
        uploadedPhotoKeys,
        req.body.features,
        verifiedStatusId,
        loginId,
        client
      );

      await client.query("COMMIT");
      return property;
    } catch (err: any) {
      await client.query("ROLLBACK");
      const cleanupKeys = [uploadedQrKey, ...uploadedPhotoKeys].filter(Boolean);
      await Promise.all(cleanupKeys.map((key) => deleteFileFromS3(key!)));
      throw new Error(err.message);
    } finally {
      client.release();
    }
  }

  async updatePropertyStatus(id: number, status: string) {
    // Validate and map status
    const { rows } = await db.query("SELECT id FROM look.status WHERE LOWER(name) = LOWER($1) LIMIT 1", [status]);
    if (!rows.length) throw new Error(`Status '${status}' not found.`);
    const statusId = rows[0].id;
    const updated = await this.repo.updateStatus(id, statusId);
    if (updated === 0) throw new Error("Property not found or update failed");
    return true;
  }

  async bulkUpdatePropertyStatus(ids: number[], status: string, reason?: string) {
    // Validate and map status
    const { rows } = await db.query("SELECT id FROM look.status WHERE LOWER(name) = LOWER($1) LIMIT 1", [status]);
    if (!rows.length) throw new Error(`Status '${status}' not found.`);
    const statusId = rows[0].id;
    
    const client: PoolClient = await db.connect();
    try {
      await client.query("BEGIN");
      await this.repo.bulkUpdateStatus(client, ids, statusId, reason);
      await client.query("COMMIT");
    } catch (err) {
      await client.query("ROLLBACK");
      throw err;
    } finally {
      client.release();
    }
  }

  async bulkDeleteProperties(ids: number[]) {
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error("Property IDs array is required");
    }

    const client: PoolClient = await db.connect();
    let allImageKeys: string[] = [];

    try {
      await client.query("BEGIN");

      // 1) Collect all image keys BEFORE deleting rows
      for (const id of ids) {
        const imageKeys = await this.repo.getImageKeysByPropertyId(client, id);
        allImageKeys.push(...imageKeys);
      }

      // 2) Delete children first to satisfy FKs, then delete properties
      for (const id of ids) {
        await this.repo.deleteFeaturesByPropertyId(client, id);
        await this.repo.deleteImagesByPropertyId(client, id);
        await this.repo.nullifyListingsByPropertyId(client, id);
      }

      const deletedCount = await this.repo.bulkDeleteProperties(client, ids);

      if (deletedCount === 0) {
        throw new Error("No properties found or already deleted");
      }

      await client.query("COMMIT");

      // TODO: Delete images from S3 after successful commit
      // This would require implementing S3 bulk delete

    } catch (err: any) {
      await client.query("ROLLBACK");
      throw new Error(
        err?.message || "Failed to delete properties. Please try again."
      );
    } finally {
      client.release();
    }
  }

  async togglePropertyFlag(id: number, column: string) {
    // Validate allowed columns
    const allowed = ["isFeatured", "isVerified"];
    if (!allowed.includes(column)) throw new Error("Invalid column name");

    // Get current value
    const flagRow = await this.repo.getPropertyFlag(id, column);
    if (!flagRow) throw new Error("Property not found");
    const currentValue = flagRow[column];
    const newValue = !currentValue;

    // Update flag
    const updated = await this.repo.updatePropertyFlag(id, column, newValue);
    if (updated === 0) throw new Error("Property not found or update failed");

    return `The property’s '${column === "isFeatured" ? "Featured" : "Verified"}' status has been successfully toggled to '${newValue ? "Enabled" : "Disabled"}'.`;
  }

  async deleteProperty(id: number) {
    return this.repo.deleteProperty(id);
  }

  async updatePropertyPhotos(req: Request) {
    return this.repo.updatePhotos(req);
  }

  async createPropertyNote(propertyId: number, note: string, userId: number) {
    if (!note || note.trim().length === 0) {
      throw new AppError("Note cannot be empty.", 400);
    }
    return this.noteRepo.createNoteForProperty(propertyId, note, userId);
  }

  async getNotesOfProperty(propertyId: number) {
    return this.noteRepo.getNotesByPropertyId(propertyId);
  }
}
