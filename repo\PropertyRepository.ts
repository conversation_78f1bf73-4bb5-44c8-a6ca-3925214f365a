import { PoolClient } from "pg";
import {db }from "../config/database";
import { GetAllPropertiesResponseDTO } from "../dto/property/GetAllPropertiesResponseDTO";
import { PaginationDTO } from "../dto/property/PaginationDTO";
import { PropertyDTO } from "../dto/property/PropertyDTO";
import { StatusCountDTO } from "../dto/property/StatusCountDTO";
import { PropertyQueries } from "../utils/database/queries/PropertyQueries";
import { FilterParams } from "../dto/payment/FilterParamTypes";

export class PropertyRepository {
  async getFilteredProperties(p: FilterParams): Promise<GetAllPropertiesResponseDTO> {
    const params = [
      p.statusId ?? null,
      p.propertyTypeId,
      p.locationId,
      p.listingType,
      p.pageSize,
      p.page,
    ];
    const { rows } = await db.query(
      PropertyQueries.GET_FILTERED_PROPERTIES,
      params
    );

    const { rows: statusCountsRows } = await db.query(
      PropertyQueries.GET_PROPERTY_STATUS_COUNTS,
      [p.propertyTypeId, p.locationId, p.listingType]
    );

    const statusCounts: StatusCountDTO[] = statusCountsRows.map((r: any) => ({
      status_id: r.status_id,
      status_name: r.status_name,
      count: r.count,
    }));

    if (!rows.length) {
      return {
        properties: [],
        pagination: {
          total: 0,
          totalPages: 0,
          currentPage: p.page,
          perPage: p.pageSize,
        },
        statusCounts,
      };
    }
    const { total_count, total_pages } = rows[0];
    const properties = rows.map(
      ({ total_count, total_pages, ...rest }: any) => rest
    );

    const pagination: PaginationDTO = {
      total: total_count,
      totalPages: total_pages,
      currentPage: p.page,
      perPage: p.pageSize,
    };
    return { properties, pagination, statusCounts };
  }


  async getPropertyById(id: number) {
    const { rows } = await db.query(PropertyQueries.GET_PROPERTY_DETAIL_BY_ID, [
      id,
    ]);
    return rows[0] as PropertyDTO;
  }

  async savePropertyWithAssets(
    data: any,
    imageKeys: string[],
    features: string[] | string,
    statusId: number,
    loginId: number,
    client: PoolClient
  ): Promise<PropertyDTO> {
    // -------- Step 1: Create or Update Property --------
    const fields = [
      "id",
      "code",
      "name",
      "local",
      "agencyId",
      "propertyTypeId",
      "apartmentTypeId",
      "totalRooms",
      "locationId",
      "address",
      "currencyId",
      "price",
      "size",
      "permitNo",
      "parking",
      "swimmingPools",
      "gym",
      "startDate",
      "statusId",
      "createdBy",
      "isFeatured",
      "isVerified",
      "adminNote",
      "expiryDate",
      "listingType",
      "completionStatus",
      "ownershipTypeId",
      "slug",
      "metaTitle",
      "metaDescription",
      "bedrooms",
      "bathrooms",
      "furnished",
      "permitId",
      "unitNo",
      "govtIssuedQr",
      "projectId",
      "tagLine",
    ];

    const params = fields.map((field) => data[field] ?? null);

    const { rows } = await client.query(
      PropertyQueries.CREATE_OR_UPDATE_PROPERTY(fields.length),
      params
    );
    const property = rows[0] as PropertyDTO;

    if (!property?.id) {
      throw new Error("Property creation failed: ID is missing.");
    }

    // -------- Step 2: Insert Images --------
    if (Array.isArray(imageKeys) && imageKeys.length > 0) {
      const inserts = imageKeys.map((key) =>
        client.query(PropertyQueries.INSERT_PROPERTY_IMAGE, [
          property.id,
          key,
          statusId,
          48, // mediaTypeId
          loginId,
        ])
      );
      await Promise.all(inserts);
    }

    // -------- Step 3: Insert Features --------
    const validFeatures = Array.isArray(features)
      ? features
      : typeof features === "string"
      ? String(features)
          .split(",")
          .map((f) => f.trim())
          .filter(Boolean)
      : [];

    const inserts = [];

    for (const feature of validFeatures) {
      const { rows: existing } = await client.query(
        PropertyQueries.CHECK_EXISTING_FEATURE,
        [property.id, feature]
      );

      if (existing.length === 0) {
        inserts.push(
          client.query(PropertyQueries.INSERT_FEATURE, [
            property.id,
            feature,
            statusId,
            data.createdBy,
          ])
        );
      }
    }

    await Promise.all(inserts);

    return property;
  }

  async updateStatus(id: number, statusId: number): Promise<number> {
    const result = await db.query(PropertyQueries.UPDATE_PROPERTY_STATUS, [
      statusId,
      id,
    ]);
    return result.rowCount ?? 0;
  }

  async bulkUpdateStatus(client: PoolClient, ids: number[], statusId: number, reason?: string): Promise<number> {
    const result = await client.query(PropertyQueries.BULK_UPDATE_PROPERTY_STATUS, [
      statusId,
      ids,
      reason,
    ]);
    return result.rowCount ?? 0;
  }

  async getPropertyFlag(id: number, column: string): Promise<any> {
    const result = await db.query(PropertyQueries.GET_PROPERTY_FLAG(column), [id]);
    return result.rows[0];
  }

  async updatePropertyFlag(id: number, column: string, newValue: boolean): Promise<number> {
  const result = await db.query(PropertyQueries.UPDATE_PROPERTY_FLAG(column), [newValue, id]);
  return result.rowCount ?? 0;
  }

  async deleteProperty(id: number): Promise<void> {
    const result = await db.query(PropertyQueries.DELETE_PROPERTY_BY_ID, [id]);
    if (result.rowCount === 0)
      throw new Error("Property not found or already deleted");
  }

  async updatePhotos(req: any): Promise<PropertyDTO | null> {
    return this.getPropertyById(Number(req.params.propertyId));
  }

  async getImageKeysByPropertyId(client: PoolClient, propertyId: number): Promise<string[]> {
    const result = await client.query(
      `SELECT "imageUrl" FROM agn.images WHERE "propertyId" = $1`,
      [propertyId]
    );
    return result.rows.map((r: any) => r.imageUrl as string).filter(Boolean);
  }

  async deleteFeaturesByPropertyId(client: PoolClient, propertyId: number): Promise<number> {
    const result = await client.query(
      `DELETE FROM agn.features WHERE "propertyId" = $1`,
      [propertyId]
    );
    return result.rowCount ?? 0;
  }

  async deleteImagesByPropertyId(client: PoolClient, propertyId: number): Promise<number> {
    const result = await client.query(
      `DELETE FROM agn.images WHERE "propertyId" = $1`,
      [propertyId]
    );
    return result.rowCount ?? 0;
  }

  async nullifyListingsByPropertyId(client: PoolClient, propertyId: number): Promise<number> {
    const result = await client.query(
      `UPDATE agn.listings SET "propertyId" = NULL WHERE "propertyId" = $1`,
      [propertyId]
    );
    return result.rowCount ?? 0;
  }

  async bulkDeleteProperties(client: PoolClient, ids: number[]): Promise<number> {
    const result = await client.query(PropertyQueries.BULK_DELETE_PROPERTIES, [ids]);
    return result.rowCount ?? 0;
  }
}
