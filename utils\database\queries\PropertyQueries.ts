import { TABLE } from "../table";

export const PropertyQueries = {
  // Status & Filtering
  FIND_STATUS_ID_BY_NAME: `
    SELECT id FROM ${TABLE.STATUS} WHERE LOWER(name) = LOWER($1) LIMIT 1
  `,
  GET_FILTERED_PROPERTIES: `
    WITH filtered AS (
      SELECT
        p."id",
        p."name",
        p."price",
        p."size",
        l."name" AS location_name,
        pt."name" AS property_type_name,
        p."listingType" AS listing_type,
        p."statusId" AS status_id,
    a."name" AS agency_name,
        s."name" AS status_name,
        p."isFeatured" AS is_featured,
        p."isVerified" AS is_verified,
        p."expiryDate" AS expiry_date,
        p."slug",
        p."metaTitle" AS meta_title,
        COALESCE(imgs.images, '[]'::JSON) AS images
      FROM agn.properties p
      LEFT JOIN look.status s ON p."statusId" = s."id"
      LEFT JOIN list.location l ON p."locationId" = l."id"
      LEFT JOIN look.type pt ON p."propertyTypeId" = pt."id"
       LEFT JOIN agn.agencies a ON p."agencyId" = a."id"
      LEFT JOIN LATERAL (
        SELECT json_agg(json_build_object('id', i.id, 'url', CONCAT('https://fmafiles-main.s3.me-central-1.amazonaws.com/', i."imageUrl"))) AS images
        FROM agn.images i
        WHERE i."propertyId" = p."id"
      ) imgs ON TRUE
      WHERE
        ($1::INT IS NULL OR p."statusId" = $1)
        AND ($2::INT IS NULL OR p."propertyTypeId" = $2)
        AND ($3::INT IS NULL OR p."locationId" = $3)
        AND ($4::INT IS NULL OR p."listingType" = $4)
        AND ($5::TEXT IS NULL OR LOWER(p."name") LIKE LOWER(CONCAT('%', $5, '%')) OR LOWER(l."name") LIKE LOWER(CONCAT('%', $5, '%')) OR LOWER(a."name") LIKE LOWER(CONCAT('%', $5, '%')))
      ORDER BY p."createdOn" DESC
    )
    SELECT *,
      (SELECT COUNT(*) FROM filtered) AS total_count,
      CEIL((SELECT COUNT(*) FROM filtered)::DECIMAL / $6)::INTEGER AS total_pages
    FROM filtered
    OFFSET (($7 - 1) * $6) LIMIT $6
  `,
  GET_PROPERTY_STATUS_COUNTS: `
    SELECT
      s.id AS status_id,
      s.name AS status_name,
      COUNT(p.id)::INTEGER AS count
    FROM agn.properties p
    JOIN look.status s ON p."statusId" = s.id
    LEFT JOIN list.location l ON p."locationId" = l."id"
    LEFT JOIN agn.agencies a ON p."agencyId" = a."id"
    WHERE
      ($1::INT IS NULL OR p."propertyTypeId" = $1) AND
      ($2::INT IS NULL OR p."locationId" = $2) AND
      ($3::INT IS NULL OR p."listingType" = $3) AND
      ($4::TEXT IS NULL OR LOWER(p."name") LIKE LOWER(CONCAT('%', $4, '%')) OR LOWER(l."name") LIKE LOWER(CONCAT('%', $4, '%')) OR LOWER(a."name") LIKE LOWER(CONCAT('%', $4, '%')))
    GROUP BY s.id, s.name
    ORDER BY s.name
  `,
  GET_PROPERTY_DETAIL_BY_ID: `
    SELECT * FROM get_property_detail($1)
  `,
  GET_PROPERTY_WITH_OWNER_BY_ID: `
    SELECT
      p.*,
      s.name AS status_name,
      COALESCE(imgs.images, '[]'::json) AS images,
      json_build_object(
        'id', pr.id,
        'firstName', pr."firstName",
        'middleName', pr."middleName",
        'lastName', pr."lastName",
        'email', pr."email",
        'profileImage', pr."profileImage"
      ) AS profile
    FROM agn.properties p
    LEFT JOIN look.status s ON p."statusId" = s."id"
    LEFT JOIN agn.agencies a ON p."agencyId" = a."id"
    LEFT JOIN prf.profile pr ON a."profileId" = pr."id"
    LEFT JOIN LATERAL (
      SELECT json_agg(json_build_object('id', i.id, 'url', CONCAT('https://fmafiles-main.s3.me-central-1.amazonaws.com/', i."imageUrl"))) AS images
      FROM agn.images i
      WHERE i."propertyId" = p."id"
    ) imgs ON TRUE
    WHERE p.id = $1
  `,

  // Dynamic: insert/update property
  CREATE_OR_UPDATE_PROPERTY: (count: number) =>
    `SELECT * FROM create_or_update_property(${Array.from(
      { length: count },
      (_, i) => `$${i + 1}`
    ).join(", ")})`,

  // Property Image
  INSERT_PROPERTY_IMAGE: `
    INSERT INTO agn.images ("propertyId", "imageUrl", "statusId", "mediaTypeId", "createdBy")
    VALUES ($1, $2, $3, $4, $5)
  `,

  // Property Features
  CHECK_EXISTING_FEATURE: `
    SELECT 1 FROM agn.features WHERE "propertyId" = $1 AND LOWER("featureName") = LOWER($2) LIMIT 1
  `,

  INSERT_FEATURE: `
    INSERT INTO agn.features ("propertyId", "featureName", "statusId", "createdBy", "createdOn")
    VALUES ($1, $2, $3, $4, NOW())
  `,

  // Update Status
  UPDATE_PROPERTY_STATUS: `
    UPDATE agn.properties SET "statusId" = $1, "modifiedOn" = NOW()
    WHERE id = $2
  `,

  UPDATE_PROPERTY_STATUS_WITH_BLOCKED_REASON: `
    UPDATE agn.properties SET "statusId" = $1, "blocked_reason" = $3, "modifiedOn" = NOW() WHERE id = $2
  `,

  UPDATE_PROPERTY_STATUS_WITH_UNPUBLISHED_REASON: `
    UPDATE agn.properties SET "statusId" = $1, "unpublished_reason" = $3, "modifiedOn" = NOW() WHERE id = $2
  `,

  BULK_UPDATE_PROPERTY_STATUS: `
    UPDATE agn.properties SET "statusId" = $1, "modifiedOn" = NOW() WHERE id = ANY($2)
  `,

  BULK_UPDATE_PROPERTY_STATUS_WITH_BLOCKED_REASON: `
    UPDATE agn.properties SET "statusId" = $1, "blocked_reason" = $3, "modifiedOn" = NOW() WHERE id = ANY($2)
  `,

  BULK_UPDATE_PROPERTY_STATUS_WITH_UNPUBLISHED_REASON: `
    UPDATE agn.properties SET "statusId" = $1, "unpublished_reason" = $3, "modifiedOn" = NOW() WHERE id = ANY($2)
  `,

  BULK_DELETE_PROPERTIES: `
    DELETE FROM agn.properties WHERE id = ANY($1)
  `,

  // Dynamic Toggle (getter and updater)
  GET_PROPERTY_FLAG: (column: string) =>
    `SELECT "${column}" FROM agn.properties WHERE id = $1`,

  UPDATE_PROPERTY_FLAG: (column: string) =>
    `UPDATE agn.properties SET "${column}" = $1, "modifiedOn" = NOW() WHERE id = $2`,

  // Delete Property
  DELETE_PROPERTY_BY_ID: `
    DELETE FROM agn.properties WHERE id = $1
  `,

  // --- Featured Properties (NEW) ---
  GET_FEATURED_PROPERTIES: `
    SELECT
      p."id",
      p."name",
      p."price",
      p."size",
      l."name" AS location_name,
      pt."name" AS property_type_name,
      p."listingType" AS listing_type,
      p."statusId" AS status_id,
  a."name" AS agency_name,
      s."name" AS status_name,
      p."isFeatured" AS is_featured,
      p."isVerified" AS is_verified,
      p."expiryDate" AS expiry_date,
      p."slug",
      p."metaTitle" AS meta_title,
      COALESCE(imgs.images, '[]'::JSON) AS images
    FROM agn.properties p
    LEFT JOIN look.status s ON p."statusId" = s."id"
    LEFT JOIN list.location l ON p."locationId" = l."id"
    LEFT JOIN look.type pt ON p."propertyTypeId" = pt."id"
     LEFT JOIN agn.agencies a ON p."agencyId" = a."id"
    LEFT JOIN LATERAL (
        SELECT json_agg(json_build_object('id', i.id, 'url', CONCAT('https://yourdomain.com/', i."imageUrl"))) AS images
      FROM agn.images i
      WHERE i."propertyId" = p."id"
    ) imgs ON TRUE
    WHERE
      p."isFeatured" = TRUE
    ORDER BY p."createdOn" DESC
    LIMIT $1
  `,

  GET_PUBLIC_SEARCH_PROPERTIES: `
    WITH filtered AS (
      SELECT
        p."id",
        p."name",
        p."price",
        p."size",
        l."name" AS location_name,
        pt."name" AS property_type_name,
        p."listingType" AS listing_type,
        p."statusId" AS status_id,
    a."name" AS agency_name,
        p.bedrooms,
        p.bathrooms,
        s."name" AS status_name,
        p."isFeatured" AS is_featured,
        p."isVerified" AS is_verified,
        p."expiryDate" AS expiry_date,
        p."slug",
        p."metaTitle" AS meta_title,
        COALESCE(imgs.images, '[]'::JSON) AS images
      FROM agn.properties p
      LEFT JOIN look.status s ON p."statusId" = s."id"
      LEFT JOIN list.location l ON p."locationId" = l."id"
      LEFT JOIN look.type pt ON p."propertyTypeId" = pt."id"
       LEFT JOIN agn.agencies a ON p."agencyId" = a."id"
      LEFT JOIN LATERAL (
        SELECT json_agg(json_build_object('id', i.id, 'url', CONCAT('https://yourdomain.com/', i."imageUrl"))) AS images
        FROM agn.images i
        WHERE i."propertyId" = p."id"
      ) imgs ON TRUE
      WHERE
        ($1::INT IS NULL OR p."locationId" = $1)
        AND ($2::NUMERIC IS NULL OR p."price" >= $2)
        AND ($3::NUMERIC IS NULL OR p."price" <= $3)
        AND ($4::INT IS NULL OR p."propertyTypeId" = $4)
        AND ($5::INT IS NULL OR p."bedrooms" = $5)
        AND ($6::INT IS NULL OR p."bathrooms" = $6)
    )
    SELECT *,
      (SELECT COUNT(*) FROM filtered) AS total_count,
      CEIL((SELECT COUNT(*) FROM filtered)::DECIMAL / $8)::INTEGER AS total_pages
    FROM filtered
    OFFSET (($7 - 1) * $8) LIMIT $8;
  `,

  GET_PROPERTY_BY_SLUG: `
    SELECT
      p."id",
      p."code"::TEXT,
      p."name"::TEXT,
      p."local"::TEXT,
      p."price",
      p."size",
      p."slug"::TEXT,
      p."permitNo"::TEXT,
      p."permitId"::TEXT,
      p."unitNo"::TEXT,
      p."address"::TEXT,
      p."bedrooms",
      p."bathrooms",
      p."totalRooms",
      p."furnished",
      p."parking",
      p."swimmingPools",
      p."gym",
      p."startDate"::TIMESTAMP,
      p."expiryDate"::TIMESTAMP,
      p."isFeatured",
      p."isVerified",
      p."adminNote"::TEXT,
      p."metaTitle"::TEXT,
      p."metaDescription"::TEXT,
      p."tagLine"::TEXT,
      p."govtIssuedQr"::TEXT,
      p."createdOn"::TIMESTAMP,
      p."modifiedOn"::TIMESTAMP,

      json_build_object('id', s."id", 'name', s."name")::JSON AS status,
      json_build_object('id', a."id", 'name', a."name")::JSON AS agency,
      json_build_object('id', l."id", 'name', l."name")::JSON AS location,
      json_build_object('id', pt."id", 'name', pt."name")::JSON AS property_type,
      json_build_object('id', at."id", 'name', at."name")::JSON AS apartment_type,
      json_build_object('id', ot."id", 'name', ot."name")::JSON AS ownership_type,
      json_build_object('id', cur."id", 'name', cur."name")::JSON AS currency,
      json_build_object('id', lt."id", 'name', lt."name")::JSON AS listing_type,
      json_build_object('id', cs."id", 'name', cs."name")::JSON AS completion_status,

      (
        SELECT json_agg(json_build_object('id', img."id", 'url', CONCAT('https://yourdomain.com/', img."imageUrl")))
        FROM agn.images img
        WHERE img."propertyId" = p."id"
      )::JSON AS images,

      (
        SELECT ARRAY_AGG(DISTINCT f."featureName")
        FROM agn.features f
        WHERE f."propertyId" = p."id"
      ) AS features

    FROM agn.properties p
    LEFT JOIN look.status s ON p."statusId" = s."id"
    LEFT JOIN agn.agencies a ON p."agencyId" = a."id"
    LEFT JOIN list.location l ON p."locationId" = l."id"
    LEFT JOIN look.type pt ON p."propertyTypeId" = pt."id"
    LEFT JOIN look.type at ON p."apartmentTypeId" = at."id"
    LEFT JOIN look.type ot ON p."ownershipTypeId" = ot."id"
    LEFT JOIN list.currency cur ON p."currencyId" = cur."id"
    LEFT JOIN look.type lt ON p."listingType" = lt."id"
    LEFT JOIN look.type cs ON p."completionStatus" = cs."id"
    WHERE p."slug" = $1
    LIMIT 1;
    `,

  // ✅ Get agent ID from profile ID
  GET_AGENCY_ID_BY_PROFILE_ID: `
    SELECT id FROM agn.agencies WHERE "profileId" = $1 LIMIT 1
  `,

  // ✅ Get properties by agent (createdBy = agentId)
  GET_PROPERTIES_BY_AGENT_ID: `
    SELECT
      p."id",
      p."name",
      p."price",
      p."size",
      l."name" AS location_name,
      pt."name" AS property_type_name,
      p."listingType" AS listing_type,
      p."statusId" AS status_id,
  a."name" AS agency_name,
      p.bedrooms,
      p.bathrooms,
      s."name" AS status_name,
      p."isFeatured" AS is_featured,
      p."isVerified" AS is_verified,
      p."expiryDate" AS expiry_date,
      p."slug",
      p."metaTitle" AS meta_title,
      COALESCE(imgs.images, '[]'::JSON) AS images
    FROM agn.properties p
    LEFT JOIN look.status s ON p."statusId" = s."id"
    LEFT JOIN list.location l ON p."locationId" = l."id"
    LEFT JOIN look.type pt ON p."propertyTypeId" = pt."id"
     LEFT JOIN agn.agencies a ON p."agencyId" = a."id"
    LEFT JOIN LATERAL (
        SELECT json_agg(json_build_object('id', i.id, 'url', CONCAT('https://yourdomain.com/', i."imageUrl"))) AS images
      FROM agn.images i
      WHERE i."propertyId" = p."id"
    ) imgs ON TRUE
    WHERE p."agencyId" = $1
    ORDER BY p."createdOn" DESC
  `,

  // ✅ Get all agents
  GET_ALL_AGENTS: `
    SELECT 
      a.id,
      a."name",
      a."profileId"
    FROM agn.agencies a
    ORDER BY a."name" ASC
  `,

  // fetch keys for S3 cleanup (optional step)
  GET_IMAGE_KEYS_BY_IDS: `
    SELECT "imageUrl"
    FROM agn.images
    WHERE "propertyId" = $1
      AND id = ANY($2::int[]);
  `,

  // hard delete/unlink rows for this property
  DELETE_IMAGES_BY_IDS: `
    DELETE FROM agn.images
    WHERE "propertyId" = $1
      AND id = ANY($2::int[]);
  `,

  INSERT_PROPERTY_IMAGE_BULK: `INSERT INTO agn.images ("propertyId","imageUrl","statusId","mediaTypeId","createdBy") SELECT $1, key, $2, $3, $4 FROM UNNEST($5::text[]) AS key;`,

  INSERT_FEATURES_IF_NOT_EXISTS_BULK: `
    WITH input_features AS (
      SELECT DISTINCT NULLIF(TRIM(f), '') AS f
      FROM UNNEST($4::text[]) AS t(f)
      WHERE NULLIF(TRIM(f), '') IS NOT NULL
    ),
    missing AS (
      SELECT i.f
      FROM input_features i
      LEFT JOIN agn.features af
        ON af."propertyId" = $1
      AND af."featureName" = i.f
      WHERE af."propertyId" IS NULL
    )
    INSERT INTO agn.features ("propertyId","featureName","statusId","createdBy")
    SELECT $1, m.f, $2, $3
    FROM missing m;
  `,

  GET_IMAGE_KEYS_BY_PROPERTY_ID: `
    SELECT "imageUrl"
    FROM agn.images
    WHERE "propertyId" = $1;
  `,

  DELETE_FEATURES_BY_PROPERTY_ID: `
    DELETE FROM agn.features
    WHERE "propertyId" = $1;
  `,

  DELETE_IMAGES_BY_PROPERTY_ID: `
    DELETE FROM agn.images
    WHERE "propertyId" = $1;
  `,

  DELETE_LISTINGS_BY_PROPERTY_ID: `
    DELETE FROM agn.listings
    WHERE "propertyId" = $1;
  `,

  NULLIFY_LISTINGS_BY_PROPERTY_ID: `
    UPDATE agn.listings
    SET "propertyId" = NULL
    WHERE "propertyId" = $1;
  `,

  // Filter Options Queries
  GET_PROPERTY_TYPES: `
    SELECT c.id, c.name 
    FROM look.type p 
    JOIN look.type c ON c."parentId" = p.id 
    WHERE LOWER(p.name) = 'property_types' 
    ORDER BY c.name
  `,
  GET_LOCATIONS: `
    SELECT id, name FROM list.location ORDER BY name
  `,
  GET_LISTING_TYPES: `
    SELECT id, "listingType" as name FROM agn.listings ORDER BY "listingType"
  `,
  GET_AGENTS: `
    SELECT id, name FROM agn.agencies ORDER BY name
  `,
  GET_STATUSES: `
    SELECT id, name FROM look.status WHERE name IN ('Available','Sold','Rented','Unpublished','Blocked') ORDER BY name
  `,
};
