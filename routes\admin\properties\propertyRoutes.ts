import { Router } from "express";
import { storageData } from "../../../utils/services/multer";
import { AdminPropertyController } from "../../../controller/admin/properties/PropertyController";

const adminPropertyController = new AdminPropertyController();
const router = Router();
const upload = storageData("documents");

// GET filter options (specific route first)
router.get("/filters", adminPropertyController.getFilterOptions);

// GET notes of a property
router.get("/note/:id", adminPropertyController.getNotes);

// PUT bulk update property status
router.put("/bulk/status", upload.none(), adminPropertyController.bulkUpdateStatus);

// DELETE bulk delete properties
router.delete("/bulk", adminPropertyController.bulkDeleteProperties);

// GET all properties with pagination and filters
router.get("/", adminPropertyController.getAllProperties);

// GET property by ID
router.get("/:id", adminPropertyController.getPropertyById);

// PUT update property status
router.put("/:id/status", upload.none(), adminPropertyController.updateStatus);

// DELETE a property
router.delete("/:id", adminPropertyController.deleteProperty);

// CREATE a note for a property
router.post("/:id/notes", upload.none(), adminPropertyController.createNote);

export default router;
