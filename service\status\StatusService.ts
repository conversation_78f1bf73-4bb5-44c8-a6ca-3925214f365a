import { StatusRepository as LookStatusRepository } from "../../repo/look/StatusRepository";
import { StatusRepository } from "../../repo/status/StatusRepository";

export class StatusService {
  private statusRepository = new LookStatusRepository();
  private repo = new StatusRepository();

  async getStatusDetails(status: string[]): Promise<any> {
    return this.statusRepository.getStatusDetailsByStatusName(status);
  }

  async getByIdStatusDetails(status: number): Promise<any> {
    return this.statusRepository.getStatusDetailsById(status);
  }

  // New wrappers for controller convenience
  async getAllStatuses() {
    return this.repo.getAll();
  }

  async searchAgencies(searchTerm: string) {
    return this.repo.searchAgencies(searchTerm);
  }
}
