import type { Knex } from "knex";


export function up(knex: Knex): Promise<void> {
  return knex.schema.alterTable('agn.properties', function(table) {
    table.text('blocked_reason').nullable().comment('Reason for blocking the property');
    table.text('unpublished_reason').nullable().comment('Reason for unpublishing the property');
  });
}

export function down(knex: Knex): Promise<void> {
  return knex.schema.alterTable('agn.properties', function(table) {
    table.dropColumn('blocked_reason');
    table.dropColumn('unpublished_reason');
  });
}