import { PoolClient } from "pg";
import { db } from "../../config/database";
import { PropertyQueries } from "../../utils/database/queries/PropertyQueries";
import { AUTH } from "../../utils/database/queries/auth";
import { PropertyDTO } from "../../dto/property/PropertyDTO";
import { GetAllPropertiesResponseDTO } from "../../dto/property/GetAllPropertiesResponseDTO";
import { StatusCountDTO } from "../../dto/property/StatusCountDTO";
import { PaginationDTO } from "../../dto/property/PaginationDTO";
import { FilterParams } from "../../dto/payment/FilterParamTypes";

export type CreateOrUpdatePayload = Record<string, any>;
export type CreateOrUpdateResult = { id: number } & PropertyDTO;

export class PropertyRepository {
  async findStatusIdByName(name?: string | null): Promise<number | null> {
    if (!name || name.toLowerCase() === "all") return null;
    const { rows } = await db.query(PropertyQueries.FIND_STATUS_ID_BY_NAME, [
      name,
    ]);
    return rows[0]?.id ?? null;
  }

  async getAgencyIdByProfileId(profileId?: number | null) {
    const { rows } = await db.query(
      `SELECT id FROM agn.agencies WHERE "profileId" = $1 LIMIT 1`,
      [profileId]
    );
    if (!rows[0]?.id) throw new Error("Agency not found for profile");
    return rows[0].id;
  }

  async getAccountTypeByProfileId(profileId?: number | null) {
    const { rows } = await db.query(AUTH.SELECT_BY_ID, [profileId]);
    return rows[0]?.accountType ?? null;
  }

  async getVerifiedStatusId(statusNames: string[]) {
    const { rows } = await db.query(
      AUTH.SELECT_ACCOUNT_STATUS(statusNames),
      statusNames
    );
    return rows[0]?.id ?? null;
  }

  async getPendingStatusId() {
    const { rows } = await db.query(PropertyQueries.FIND_STATUS_ID_BY_NAME, [
      "Pending",
    ]);
    return rows[0]?.id ?? null;
  }

  async getLoginIdByProfileId(profileId?: number | null) {
    const { rows } = await db.query(AUTH.SELECT_BY_PROFILE_ID_FROM_LOGIN, [
      profileId,
    ]);
    return rows[0]?.id ?? null;
  }

  // --- filter option helpers ---
  async getPropertyTypes(): Promise<{ id: number; name: string }[]> {
    const { rows } = await db.query(PropertyQueries.GET_PROPERTY_TYPES);
    return rows;
  }
async getTypesByParentName(parentName: string) {
  const { rows } = await db.query(
    `SELECT c.id, c.name 
     FROM look.type p 
     JOIN look.type c ON c."parentId" = p.id 
     WHERE LOWER(p.name) = LOWER($1) 
     ORDER BY c.name`,
    [parentName]
  );
  return rows.map((r: any) => ({ id: r.id, name: r.name }));
}

  // --- lists ---
  async getFilteredProperties(
    p: FilterParams
  ): Promise<GetAllPropertiesResponseDTO> {
    const params = [
      p.statusId ?? null,
      p.propertyTypeId,
      p.locationId,
      p.listingType,
      p.search,
      p.pageSize,
      p.page,
    ];
    const { rows } = await db.query(
      PropertyQueries.GET_FILTERED_PROPERTIES,
      params
    );

    const { rows: statusCountsRows } = await db.query(
      PropertyQueries.GET_PROPERTY_STATUS_COUNTS,
      [p.propertyTypeId, p.locationId, p.listingType, p.search]
    );

    const statusCounts: StatusCountDTO[] = statusCountsRows.map((r: any) => ({
      status_id: r.status_id,
      status_name: r.status_name,
      count: r.count,
    }));

    if (!rows.length) {
      return {
        properties: [],
        pagination: {
          total: 0,
          totalPages: 0,
          currentPage: p.page,
          perPage: p.pageSize,
        },
        statusCounts,
      };
    }
    const { total_count, total_pages } = rows[0];
    const properties = rows.map(
      ({ total_count, total_pages, ...rest }: any) => rest
    );

    const pagination: PaginationDTO = {
      total: total_count,
      totalPages: total_pages,
      currentPage: p.page,
      perPage: p.pageSize,
    };
    return { properties, pagination, statusCounts };
  }

  async getPropertyById(id: number) {
    const { rows } = await db.query(PropertyQueries.GET_PROPERTY_DETAIL_BY_ID, [
      id,
    ]);
    return rows[0] as PropertyDTO;
  }

  async getPropertyWithOwnerById(id: number) {
    const { rows } = await db.query(
      PropertyQueries.GET_PROPERTY_WITH_OWNER_BY_ID,
      [id]
    );
    return rows[0];
  }

  async getPropertyBySlug(slug: string) {
    const { rows } = await db.query(PropertyQueries.GET_PROPERTY_BY_SLUG, [
      slug,
    ]);
    return rows[0] as PropertyDTO;
  }

  // --- create/update core row (no loops) ---
  async createOrUpdateProperty(
    client: PoolClient,
    data: CreateOrUpdatePayload
  ): Promise<CreateOrUpdateResult> {
    const fields = [
      "id",
      "code",
      "name",
      "local",
      "agencyId",
      "propertyTypeId",
      "apartmentTypeId",
      "totalRooms",
      "locationId",
      "address",
      "currencyId",
      "price",
      "size",
      "permitNo",
      "parking",
      "swimmingPools",
      "gym",
      "startDate",
      "statusId",
      "createdBy",
      "isFeatured",
      "isVerified",
      "adminNote",
      "expiryDate",
      "listingType",
      "completionStatus",
      "ownershipTypeId",
      "slug",
      "metaTitle",
      "metaDescription",
      "bedrooms",
      "bathrooms",
      "furnished",
      "permitId",
      "unitNo",
      "govtIssuedQr",
      "projectId",
      "tagLine",
    ];
    const params = fields.map((f) => data[f] ?? null);
    const { rows } = await client.query(
      PropertyQueries.CREATE_OR_UPDATE_PROPERTY(fields.length),
      params
    );
    return rows[0] as CreateOrUpdateResult;
  }

  // --- bulk inserts (single SQL each, no loops in TS) ---
  async insertPropertyImagesBulk(
    client: PoolClient,
    args: {
      propertyId: number;
      statusId: number;
      mediaTypeId: number;
      loginId: number;
      keys: string[];
    }
  ) {
    if (!args.keys.length) return;
    await client.query(PropertyQueries.INSERT_PROPERTY_IMAGE_BULK, [
      args.propertyId,
      args.statusId,
      args.mediaTypeId,
      args.loginId,
      args.keys, // UNNEST/text[]
    ]);
  }

  async upsertFeaturesBulk(
    client: PoolClient,
    args: {
      propertyId: number;
      statusId: number;
      createdBy: number;
      features: string[];
    }
  ) {
    if (!args.features.length) return;
    await client.query(PropertyQueries.INSERT_FEATURES_IF_NOT_EXISTS_BULK, [
      args.propertyId,
      args.statusId,
      args.createdBy,
      args.features, // UNNEST/text[]
    ]);
  }

  // --- status/flag updates ---
  async updateStatusById(id: number, statusId: number, reason?: string) {
    let query = PropertyQueries.UPDATE_PROPERTY_STATUS;
    let params: any[] = [statusId, id];

    if (reason) {
      if (statusId === 32) { // Blocked
        query = PropertyQueries.UPDATE_PROPERTY_STATUS_WITH_BLOCKED_REASON;
        params = [statusId, id, reason];
      } else if (statusId === 27) { // Unpublished
        query = PropertyQueries.UPDATE_PROPERTY_STATUS_WITH_UNPUBLISHED_REASON;
        params = [statusId, id, reason];
      }
    }

    await db.query(query, params);
  }

  async bulkUpdateStatusByIds(client: PoolClient, ids: number[], statusId: number, reason?: string) {
    let query = PropertyQueries.BULK_UPDATE_PROPERTY_STATUS;
    let params: any[] = [statusId, ids];

    if (reason) {
      if (statusId === 32) { // Blocked
        query = PropertyQueries.BULK_UPDATE_PROPERTY_STATUS_WITH_BLOCKED_REASON;
        params = [statusId, ids, reason];
      } else if (statusId === 27) { // Unpublished
        query = PropertyQueries.BULK_UPDATE_PROPERTY_STATUS_WITH_UNPUBLISHED_REASON;
        params = [statusId, ids, reason];
      }
    }

    await client.query(query, params);
  }

  async toggleFlag(id: number, column: "isFeatured" | "isVerified") {
    const res = await db.query(PropertyQueries.GET_PROPERTY_FLAG(column), [id]);
    if (res.rowCount === 0) throw new Error("Property not found");
    const newValue = !res.rows[0][column];
    await db.query(PropertyQueries.UPDATE_PROPERTY_FLAG(column), [
      newValue,
      id,
    ]);
    return `The property’s '${
      column === "isFeatured" ? "Featured" : "Verified"
    }' status has been toggled to '${newValue ? "Enabled" : "Disabled"}'.`;
  }

  async deleteProperty(client: PoolClient, id: number) {
    const r = await client.query(PropertyQueries.DELETE_PROPERTY_BY_ID, [id]);
    return (r.rowCount ?? 0) > 0;
  }

  async getFeaturedProperties(limit: number) {
    const { rows } = await db.query(PropertyQueries.GET_FEATURED_PROPERTIES, [
      limit,
    ]);
    return rows;
  }

  async searchPublicProperties(args: {
    location: number | null;
    minPrice: number | null;
    maxPrice: number | null;
    type: number | null;
    bedrooms: number | null;
    bathrooms: number | null;
    page: number;
    pageSize: number;
  }) {
    const params = [
      args.location,
      args.minPrice,
      args.maxPrice,
      args.type,
      args.bedrooms,
      args.bathrooms,
      args.page,
      args.pageSize,
    ];
    const { rows } = await db.query(
      PropertyQueries.GET_PUBLIC_SEARCH_PROPERTIES,
      params
    );
    if (!rows.length) {
      return {
        properties: [],
        pagination: {
          total: 0,
          totalPages: 0,
          currentPage: args.page,
          perPage: args.pageSize,
        },
      };
    }
    const { total_count, total_pages } = rows[0];
    return {
      properties: rows,
      pagination: {
        total: total_count,
        totalPages: total_pages,
        currentPage: args.page,
        perPage: args.pageSize,
      },
    };
  }

  // fetch file keys for S3 cleanup (optional)
  async getImageKeysByIds(
    client: PoolClient,
    propertyId: number,
    ids: number[]
  ) {
    const { rows } = await client.query(PropertyQueries.GET_IMAGE_KEYS_BY_IDS, [
      propertyId,
      ids,
    ]);
    return rows.map((r: any) => r.imageUrl as string);
  }

  // unlink/delete rows in agn.images for this property
  async deleteImagesByIds(
    client: PoolClient,
    propertyId: number,
    ids: number[]
  ) {
    await client.query(PropertyQueries.DELETE_IMAGES_BY_IDS, [propertyId, ids]);
  }

  async getImageKeysByPropertyId(
    client: PoolClient,
    propertyId: number
  ): Promise<string[]> {
    const { rows } = await client.query(
      PropertyQueries.GET_IMAGE_KEYS_BY_PROPERTY_ID,
      [propertyId]
    );
    return rows.map((r: any) => r.imageUrl as string).filter(Boolean);
  }

  async deleteFeaturesByPropertyId(
    client: PoolClient,
    propertyId: number
  ): Promise<number> {
    const { rowCount } = await client.query(
      PropertyQueries.DELETE_FEATURES_BY_PROPERTY_ID,
      [propertyId]
    );
    return rowCount ?? 0;
  }

  async deleteImagesByPropertyId(
    client: PoolClient,
    propertyId: number
  ): Promise<number> {
    const { rowCount } = await client.query(
      PropertyQueries.DELETE_IMAGES_BY_PROPERTY_ID,
      [propertyId]
    );
    return rowCount ?? 0;
  }

  async deleteListingsByPropertyId(
    client: PoolClient,
    propertyId: number
  ): Promise<number> {
    const { rowCount } = await client.query(
      PropertyQueries.DELETE_LISTINGS_BY_PROPERTY_ID,
      [propertyId]
    );
    return rowCount ?? 0;
  }

  async nullifyListingsByPropertyId(
    client: PoolClient,
    propertyId: number
  ): Promise<number> {
    const { rowCount } = await client.query(
      PropertyQueries.NULLIFY_LISTINGS_BY_PROPERTY_ID,
      [propertyId]
    );
    return rowCount ?? 0;
  }

  // Filter Options Methods
  async getLocations(): Promise<{ id: number; name: string }[]> {
    const { rows } = await db.query(PropertyQueries.GET_LOCATIONS);
    return rows;
  }

  async getListingTypes(): Promise<{ id: number; name: string }[]> {
    const { rows } = await db.query(PropertyQueries.GET_LISTING_TYPES);
    return rows;
  }

  async getAgents(): Promise<{ id: number; name: string }[]> {
    const { rows } = await db.query(PropertyQueries.GET_AGENTS);
    return rows;
  }

  async getStatuses(): Promise<{ id: number; name: string }[]> {
    const { rows } = await db.query(PropertyQueries.GET_STATUSES);
    return rows;
  }

  async bulkDeleteProperties(client: PoolClient, ids: number[]): Promise<number> {
    const result = await client.query(PropertyQueries.BULK_DELETE_PROPERTIES, [ids]);
    return result.rowCount ?? 0;
  }
}
