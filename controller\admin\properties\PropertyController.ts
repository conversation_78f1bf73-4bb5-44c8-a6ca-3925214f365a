import { Request, Response } from "express";
import { PropertyService } from "../../../service/properties/PropertyService";
import asyncHandler from "../../../middleware/trycatch";
import { db } from "../../../config/database";
import { error, response, responseData } from "../../../utils/response";
import { TABLE } from "../../../utils/database/table";
import { sendPropertyStatusUpdateEmail } from "../../../utils/services/nodemailer/sendPropertyStatusUpdateEmail";

export class AdminPropertyController {
  private propertyService = new PropertyService();

  getAllProperties = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.propertyService.getAllProperties(req.query);
    return responseData(res, 200, "Properties fetched successfully", result);
  });

  getPropertyById = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.propertyService.getPropertyById(
      Number(req.params.id)
    );
    return responseData(res, 200, "Property fetched successfully", result);
  });

  updateStatus = asyncHandler(async (req: Request, res: Response) => {
     const { status, reason } = req.body;
     console.log('req.body:', req.body);
     console.log('status:', status);

    // Check if status is "Blocked" or "Unpublished" (case-insensitive)
    if (status && (status.toLowerCase() === "blocked" || status.toLowerCase() === "unpublished")) {
      // Validate that reason is provided and not empty
      if (!reason || typeof reason !== "string" || reason.trim() === "") {
        return response(res, 400, "Reason is required when setting status to Blocked or Unpublished.");
      }
    }

    // Fetch property with owner info before updating status
    const property = await this.propertyService.getPropertyWithOwnerById(
      Number(req.params.id)
    );

    await this.propertyService.updatePropertyStatus(
      Number(req.params.id),
      status,
      reason // Pass reason to the service layer
    );

    // Send email notification if property is blocked
    if (status && status.toLowerCase() === "blocked" && property?.profile?.email) {
      const ownerName = property.profile?.firstName
        ? `${property.profile.firstName} ${property.profile.lastName || ""}`.trim()
        : property.profile?.firstName || "Property Owner";

      try {
        await sendPropertyStatusUpdateEmail(
          property.name || "Your Property",
          property.id,
          ownerName,
          property.profile.email,
          status,
          reason
        );
      } catch (emailError) {
        console.error("Failed to send property status update email:", emailError);
        // Don't throw error to avoid disrupting the status update flow
      }
    }

    return response(res, 200, "Status updated successfully");
  });

  toggleFlag = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.propertyService.togglePropertyFlag(
      Number(req.params.id),
      req.body.column
    );
    return response(res, 200, result);
  });

  deleteProperty = asyncHandler(async (req: Request, res: Response) => {
    await this.propertyService.deleteProperty(Number(req.params.id));
    return response(res, 200, "Property deleted successfully.");
  });

  createNote = asyncHandler(async (req: Request, res: Response) => {
    const propertyId = Number(req.params.id);
    const { note } = req.body;

    const result = await this.propertyService.createPropertyNote(
      propertyId,
      note,
      req.user?.id || 1
    );

    return responseData(res, 201, "Note added successfully.", result);
  });

  getNotes = asyncHandler(async (req: Request, res: Response) => {
    const propertyId = Number(req.params.id);
    const result = await this.propertyService.getNotesOfProperty(propertyId);
    return responseData(res, 200, "Notes fetched successfully.", result);
  });

  getFilterOptions = asyncHandler(async (req: Request, res: Response) => {
    const result = await this.propertyService.getFilterOptions();
    return responseData(res, 200, "Filter options fetched successfully", result);
  });

  bulkDeleteProperties = asyncHandler(async (req: Request, res: Response) => {
    const { ids } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return response(res, 400, "Property IDs array is required");
    }

    // Validate and convert IDs to numbers
    const validIds: number[] = [];
    for (const id of ids) {
      const numId = Number(id);
      if (!Number.isFinite(numId) || numId <= 0) {
        return response(res, 400, `Invalid property ID: ${id}`);
      }
      validIds.push(numId);
    }

    await this.propertyService.bulkDeleteProperties(validIds);
    return response(res, 200, `Successfully deleted ${validIds.length} properties`);
  });

  bulkUpdateStatus = asyncHandler(async (req: Request, res: Response) => {
    const { ids, status, reason } = req.body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return response(res, 400, "Property IDs array is required");
    }
    
    if (!status) {
      return response(res, 400, "Status is required");
    }

    // Validate and convert IDs to numbers
    const validIds: number[] = [];
    for (const id of ids) {
      const numId = Number(id);
      if (!Number.isFinite(numId) || numId <= 0) {
        return response(res, 400, `Invalid property ID: ${id}`);
      }
      validIds.push(numId);
    }

    await this.propertyService.bulkUpdatePropertyStatus(validIds, status, reason);
    return response(res, 200, `Status updated successfully for ${validIds.length} properties`);
  });

}
